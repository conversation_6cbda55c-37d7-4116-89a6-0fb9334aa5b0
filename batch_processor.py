import io
import os
import re
import sys
from pathlib import Path
from datetime import timedelta

import pandas as pd
import numpy as np

# ============================== CONFIG =============================== #

# Put Blast CSVs inside these folders (in the same directory as this script)
BLAST_FOLDERS = ["BlastTeam1", "BlastTeam2"]

# Time window for a match between Blast and HitTrax timestamps (seconds)
TIME_WINDOW_SEC = 5

# Fuzzy threshold for mapping Blast file name to a HitTrax roster name
FUZZY_NAME_THRESHOLD = 85  # 0..100

# --------------------------------------------------------------------- #

def info(msg): print(msg, flush=True)
def warn(msg): print(f"   ⚠️ {msg}", flush=True)
def err(msg):  print(f"   ❌ {msg}", flush=True)

def sanitize_columns(cols):
    out = []
    for c in cols:
        c = str(c)
        c = c.replace("“","\"").replace("”","\"").replace("’","'")
        c = c.replace("–","-").replace("—","-")
        c = re.sub(r"\s+", " ", c).strip().strip('"').strip("'")
        out.append(c)
    return out

def canonical_name(s):
    if not isinstance(s, str): return ""
    s = s.strip()
    s = re.sub(r"\s+", " ", s)
    return " ".join(w.capitalize() for w in s.split())

def fuzzy_ratio(a, b):
    a = canonical_name(a).lower()
    b = canonical_name(b).lower()
    if not a or not b: return 0
    def bigrams(x): return set(x[i:i+2] for i in range(len(x)-1))
    A, B = bigrams(" ".join(sorted(a.split()))), bigrams(" ".join(sorted(b.split())))
    if not A or not B: return 0
    return int(round(100 * (2 * len(A & B)) / (len(A) + len(B))))

# ----------------------- TIMESTAMP HANDLING --------------------------- #

def parse_blast_timestamp(series):
    # Example Blast format: "Jul 03, 2025 03:30:48 pm"
    s = pd.to_datetime(series, errors="coerce")
    return pd.to_datetime(s.dt.strftime("%Y-%m-%d %H:%M:%S"), errors="coerce")

def parse_hittrax_timestamp(series):
    # Example HitTrax format: "7/25/2025 18:37:22"
    s = pd.to_datetime(series, errors="coerce")
    return pd.to_datetime(s.dt.strftime("%Y-%m-%d %H:%M:%S"), errors="coerce")

def best_timestamp_column(df, preferred_order):
    cols = {c.lower(): c for c in df.columns}
    for cand in preferred_order:
        if cand.lower() in cols:
            return cols[cand.lower()]
    for c in df.columns:
        if re.search(r"(date|time|timestamp)", c, flags=re.I):
            return c
    return None

# -------------------------- BLAST READER ------------------------------ #

BLAST_HEADER_SIGNATURE = [
    "Date","Equipment","Handedness","Swing Details",
    "Plane Score","Connection Score","Rotation Score",
    "Bat Speed (mph)","Rotational Acceleration (g)",
    "On Plane Efficiency (%)","Attack Angle (deg)",
    "Early Connection (deg)","Connection at Impact (deg)",
    "Vertical Bat Angle (deg)","Power (kW)",
    "Time to Contact (sec)","Peak Hand Speed (mph)",
    "Exit Velocity (mph)","Launch Angle (deg)","Estimated Distance (feet)"
]

def find_blast_header_index(lines):
    # Look for the row that starts with the "Date,Equipment,Handedness,..." header
    pat = r'^"?Date"?,\s*"?Equipment"?,\s*"?Handedness"?,\s*"?Swing Details"?'
    for i, line in enumerate(lines):
        if re.search(pat, line.strip(), flags=re.I):
            return i
    return None

def read_blast_csv_detecting_header(path: Path) -> pd.DataFrame:
    try:
        try:
            text = path.read_text(encoding="utf-8")
        except UnicodeDecodeError:
            text = path.read_text(encoding="utf-8-sig", errors="ignore")

        lines = text.splitlines()
        hdr_idx = find_blast_header_index(lines)
        if hdr_idx is None:
            warn(f"Could not find Blast header in {path.name}; skipping")
            return pd.DataFrame()

        sub = "\n".join(lines[hdr_idx:])
        df = pd.read_csv(io.StringIO(sub))
        df.columns = sanitize_columns(df.columns)

        if "Date" not in df.columns:
            warn(f"No 'Date' column in {path.name}; skipping")
            return pd.DataFrame()

        if df.shape[0] == 0:
            warn(f"{path.name}: header found but no swing rows; skipping")
            return pd.DataFrame()

        keep = [c for c in BLAST_HEADER_SIGNATURE if c in df.columns]
        df = df.loc[:, keep].copy()

        # Attach athlete from the FILE NAME (e.g., "First Last.csv")
        df["Athlete"] = canonical_name(path.stem)

        # Parse Blast timestamp
        df["Blast_Timestamp"] = parse_blast_timestamp(df["Date"])
        df = df[~df["Blast_Timestamp"].isna()].copy()

        # Dedup safe: same athlete & exact same timestamp
        df = df.drop_duplicates(subset=["Athlete", "Blast_Timestamp"], keep="first")

        df["__source_file"] = path.name
        return df

    except Exception as e:
        err(f"Could not read {path} → {e}")
        return pd.DataFrame()

# -------------------------- HITTRAX READER ---------------------------- #

LIKELY_HITTRAX_NAME_COLS = [
    # Your HitTrax files use "User"
    "User", "User Name",
    # Extras, just in case
    "Athlete","Athlete Name","Player","Player Name","Player_Name",
    "Hitter","Hitter Name","Hitter_Name","Batter","Batter Name","Batter_Name",
    "Name"
]

def choose_hittrax_name_column(df: pd.DataFrame):
    # 1) Prefer "User" explicitly (case-insensitive)
    lcmap = {c.lower(): c for c in df.columns}
    if "user" in lcmap:
        return lcmap["user"]
    if "user name" in lcmap:
        return lcmap["user name"]

    # 2) Other common labels
    for cand in LIKELY_HITTRAX_NAME_COLS:
        if cand.lower() in lcmap:
            return lcmap[cand.lower()]

    # 3) Heuristic fallback: column with the most name-like strings
    best_col, best_score = None, -1
    for c in df.columns:
        series = df[c].dropna()
        series = series[series.map(lambda x: isinstance(x, str))]
        if series.empty:
            continue
        looks_like_name = series.map(lambda x: bool(re.search(r"[A-Za-z]", x)) and (" " in x or re.search(r"[A-Za-z]{3,}", x)))
        score = looks_like_name.sum() + 0.25 * series[looks_like_name].nunique()
        if score > best_score:
            best_score = score
            best_col = c
    return best_col

def read_hittrax_csv(path: Path) -> pd.DataFrame:
    try:
        df = pd.read_csv(path)
    except UnicodeDecodeError:
        df = pd.read_csv(path, encoding="utf-8-sig")

    df.columns = sanitize_columns(df.columns)

    name_col = choose_hittrax_name_column(df)
    if not name_col:
        raise ValueError(
            f"Could not find a player/name column in HitTrax file. "
            f"Available columns: {list(df.columns)}"
        )

    # Keep a copy of raw "User" before rename (for HitTrax_User)
    df["__HitTrax_User_Raw"] = df[name_col].map(canonical_name)

    df.rename(columns={name_col: "Athlete"}, inplace=True)
    df["Athlete"] = df["Athlete"].map(canonical_name)

    # Timestamp column: try common labels
    ts_col = best_timestamp_column(
        df,
        preferred_order=["Date Time", "DateTime", "Timestamp", "Time", "Date"]
    )
    if not ts_col:
        raise ValueError(
            f"Could not find a timestamp column in HitTrax file. "
            f"Available columns: {list(df.columns)}"
        )

    df.rename(columns={ts_col: "HitTrax_Timestamp_Raw"}, inplace=True)
    df["HitTrax_Timestamp"] = parse_hittrax_timestamp(df["HitTrax_Timestamp_Raw"])
    df = df[~df["HitTrax_Timestamp"].isna()].copy()

    # Debug: show which columns we used
    uniq_names = sorted([n for n in df["Athlete"].dropna().unique().tolist() if n])
    info(f"🧾 HitTrax name column: '{name_col}' ({len(uniq_names)} unique athletes)")
    info(f"🕒 HitTrax time column: '{ts_col}'")

    return df

# ----------------------------- MATCHING ------------------------------- #

def asof_match(blast_df, hittrax_df, window_seconds=5):
    if blast_df.empty or hittrax_df.empty:
        return pd.DataFrame()
    b = blast_df.copy().sort_values("Blast_Timestamp")
    h = hittrax_df.copy().sort_values("HitTrax_Timestamp")
    matched = pd.merge_asof(
        b, h,
        left_on="Blast_Timestamp",
        right_on="HitTrax_Timestamp",
        direction="nearest",
        tolerance=pd.Timedelta(seconds=window_seconds)
    )
    matched = matched[~matched["HitTrax_Timestamp"].isna()].copy()
    if matched.empty:
        return matched
    matched["Time_Delta_Sec"] = (matched["HitTrax_Timestamp"] - matched["Blast_Timestamp"]).dt.total_seconds().abs()
    matched["Confidence"] = pd.cut(
        matched["Time_Delta_Sec"],
        bins=[-0.001, 3.0, window_seconds],
        labels=["High", "OK"]
    ).astype(str)
    matched["Match_Type"] = "Matched"
    return matched

# -------------------- NORMALIZATION & FINAL ORDER --------------------- #

# Target final column order (exactly as you requested)
FINAL_ORDER = [
    "Athlete","Match_Type","Confidence","Time_Difference_Seconds",
    "Blast_Date","Blast_Equipment","Blast_Handedness","Blast_Swing_Details",
    "Blast_Plane_Score","Blast_Connection_Score","Blast_Rotation_Score",
    "Blast_Bat_Speed_mph","Blast_Rotational_Acceleration_g","Blast_On_Plane_Efficiency_Pct",
    "Blast_Attack_Angle_deg","Blast_Early_Connection_deg","Blast_Connection_at_Impact_deg",
    "Blast_Vertical_Bat_Angle_deg","Blast_Power_kW","Blast_Time_to_Contact_sec",
    "Blast_Peak_Hand_Speed_mph","Blast_Exit_Velocity_mph","Blast_Launch_Angle_deg",
    "Blast_Estimated_Distance_feet","Blast_Source_File",
    "HitTrax_Number","HitTrax_AB","HitTrax_Date","HitTrax_Time_Stamp","HitTrax_Pitch",
    "HitTrax_Strike_Zone","HitTrax_P__Type","HitTrax_Velo","HitTrax_LA","HitTrax_Dist",
    "HitTrax_Res","HitTrax_Type","HitTrax_Horiz__Angle","HitTrax_Pts","HitTrax_Strike_Zone_Bottom",
    "HitTrax_Strike_Zone_Top","HitTrax_Strike_Zone_Width","HitTrax_Vertical_Distance",
    "HitTrax_Horizontal_Distance","HitTrax_POI_X","HitTrax_POI_Y","HitTrax_POI_Z",
    "HitTrax_Spray_Chart_X","HitTrax_Spray_Chart_Z","HitTrax_Fielded_X","HitTrax_Fielded_Z",
    "HitTrax_Bat_Material","HitTrax_User","HitTrax_Pitch_Angle","HitTrax_Batting","HitTrax_Level",
    "HitTrax_Opposing_Player","HitTrax_Tag","HitTrax_Source_File"
]

# Blast rename map (from Blast header to final names)
BLAST_RENAME_MAP = {
    "Date":"Blast_Date",
    "Equipment":"Blast_Equipment",
    "Handedness":"Blast_Handedness",
    "Swing Details":"Blast_Swing_Details",
    "Plane Score":"Blast_Plane_Score",
    "Connection Score":"Blast_Connection_Score",
    "Rotation Score":"Blast_Rotation_Score",
    "Bat Speed (mph)":"Blast_Bat_Speed_mph",
    "Rotational Acceleration (g)":"Blast_Rotational_Acceleration_g",
    "On Plane Efficiency (%)":"Blast_On_Plane_Efficiency_Pct",
    "Attack Angle (deg)":"Blast_Attack_Angle_deg",
    "Early Connection (deg)":"Blast_Early_Connection_deg",
    "Connection at Impact (deg)":"Blast_Connection_at_Impact_deg",
    "Vertical Bat Angle (deg)":"Blast_Vertical_Bat_Angle_deg",
    "Power (kW)":"Blast_Power_kW",
    "Time to Contact (sec)":"Blast_Time_to_Contact_sec",
    "Peak Hand Speed (mph)":"Blast_Peak_Hand_Speed_mph",
    "Exit Velocity (mph)":"Blast_Exit_Velocity_mph",
    "Launch Angle (deg)":"Blast_Launch_Angle_deg",
    "Estimated Distance (feet)":"Blast_Estimated_Distance_feet",
    "__source_file":"Blast_Source_File",
}

# HitTrax rename map (common raw columns -> final names)
HITTRAX_RENAME_MAP = {
    "Number":"HitTrax_Number",
    "AB":"HitTrax_AB",
    "Date":"HitTrax_Date",
    "Time Stamp":"HitTrax_Time_Stamp",
    "Pitch":"HitTrax_Pitch",
    "Strike Zone":"HitTrax_Strike_Zone",
    "P. Type":"HitTrax_P__Type",
    "P Type":"HitTrax_P__Type",  # variant without dot
    "Velo":"HitTrax_Velo",
    "LA":"HitTrax_LA",
    "Dist":"HitTrax_Dist",
    "Res":"HitTrax_Res",
    "Type":"HitTrax_Type",
    "Horiz. Angle":"HitTrax_Horiz__Angle",
    "Pts":"HitTrax_Pts",
    "Strike Zone Bottom":"HitTrax_Strike_Zone_Bottom",
    "Strike Zone Top":"HitTrax_Strike_Zone_Top",
    "Strike Zone Width":"HitTrax_Strike_Zone_Width",
    "Vertical Distance":"HitTrax_Vertical_Distance",
    "Horizontal Distance":"HitTrax_Horizontal_Distance",
    "POI X":"HitTrax_POI_X",
    "POI Y":"HitTrax_POI_Y",
    "POI Z":"HitTrax_POI_Z",
    "Spray Chart X":"HitTrax_Spray_Chart_X",
    "Spray Chart Z":"HitTrax_Spray_Chart_Z",
    "Fielded X":"HitTrax_Fielded_X",
    "Fielded Z":"HitTrax_Fielded_Z",
    "Bat Material":"HitTrax_Bat_Material",
    "User":"HitTrax_User",
    "__HitTrax_User_Raw":"HitTrax_User",  # fallback
    "Pitch Angle":"HitTrax_Pitch_Angle",
    "Batting":"HitTrax_Batting",
    "Level":"HitTrax_Level",
    "Opposing Player":"HitTrax_Opposing_Player",
    "Tag":"HitTrax_Tag",
    "Source File":"HitTrax_Source_File",
}

def _normalize_hittrax_date_time(df: pd.DataFrame):
    # If the file didn't have separate Date / Time Stamp, synthesize them from HitTrax_Timestamp
    if "HitTrax_Timestamp" in df.columns:
        if "HitTrax_Date" not in df.columns:
            try:
                df["HitTrax_Date"] = df["HitTrax_Timestamp"].dt.strftime("%Y-%m-%d")
            except Exception:
                df["HitTrax_Date"] = pd.NA
        if "HitTrax_Time_Stamp" not in df.columns:
            try:
                df["HitTrax_Time_Stamp"] = df["HitTrax_Timestamp"].dt.strftime("%H:%M:%S")
            except Exception:
                df["HitTrax_Time_Stamp"] = pd.NA
    return df

def normalize_and_order(df: pd.DataFrame) -> pd.DataFrame:
    # Remove duplicate columns (safety)
    df = df.loc[:, ~df.columns.duplicated()].copy()

    # Rename Blast fields
    for src, dst in BLAST_RENAME_MAP.items():
        if src in df.columns and dst not in df.columns:
            df.rename(columns={src: dst}, inplace=True)

    # Rename HitTrax fields
    for src, dst in HITTRAX_RENAME_MAP.items():
        if src in df.columns and dst not in df.columns:
            df.rename(columns={src: dst}, inplace=True)

    # Standard names for match metadata
    if "Time_Delta_Sec" in df.columns and "Time_Difference_Seconds" not in df.columns:
        df.rename(columns={"Time_Delta_Sec":"Time_Difference_Seconds"}, inplace=True)

    # Ensure HitTrax_User exists (fallback to Athlete)
    if "HitTrax_User" not in df.columns:
        if "Athlete" in df.columns:
            df["HitTrax_User"] = df["Athlete"]
        else:
            df["HitTrax_User"] = pd.NA

    # Fill/synthesize HitTrax date/time if missing
    df = _normalize_hittrax_date_time(df)

    # Ensure all final columns exist
    for col in FINAL_ORDER:
        if col not in df.columns:
            df[col] = pd.NA

    # Reorder exactly
    df = df[FINAL_ORDER].copy()

    return df

# ------------------------------- MAIN -------------------------------- #

def main():
    base = Path(__file__).resolve().parent
    input_folder = base
    out_folder = base / "merged_results"
    out_folder.mkdir(exist_ok=True)

    info("🚀 Starting batch processing...")
    info(f"📁 Input folder: {input_folder}")
    info(f"💾 Output folder: {out_folder}")

    # Choose the (largest) HitTrax CSV in the root
    root_csvs = [p for p in input_folder.glob("*.csv") if all(f not in p.as_posix() for f in BLAST_FOLDERS)]
    if not root_csvs:
        err("No HitTrax CSV found in the folder.")
        sys.exit(1)
    hittrax_path = max(root_csvs, key=lambda p: p.stat().st_size)
    info(f"📊 Found 1 HitTrax file → {hittrax_path.name}")

    # Read HitTrax
    hittrax = read_hittrax_csv(hittrax_path)

    # Build roster from HitTrax ("User" column normalized)
    roster = sorted([n for n in hittrax["Athlete"].dropna().unique().tolist() if n])
    info(f"👥 Roster size: {len(roster)}")

    # Find Blast CSVs inside the Blast folders
    blast_paths = []
    existing_blast_dirs = []
    for folder in BLAST_FOLDERS:
        d = input_folder / folder
        if d.exists() and d.is_dir():
            existing_blast_dirs.append(folder)
            blast_paths.extend(sorted(d.glob("*.csv")))
    if existing_blast_dirs:
        info(f"🎒 Using Blast folders: {', '.join(existing_blast_dirs)}")
    else:
        warn("No BlastTeam folders found. Continuing with HitTrax only.")

    # Map Blast file name → best roster athlete (fuzzy on file stem)
    file_to_athlete = {}
    for p in blast_paths:
        stem = canonical_name(p.stem)
        best, best_score = None, -1
        for r in roster:
            s = fuzzy_ratio(stem, r)
            if s > best_score:
                best, best_score = r, s
        if best_score >= FUZZY_NAME_THRESHOLD:
            file_to_athlete[p] = best
        else:
            # Not on roster; ignore silently
            continue

    # Read the mapped Blast files
    blast_frames = []
    for p, mapped in file_to_athlete.items():
        dfb = read_blast_csv_detecting_header(p)
        if dfb.empty:
            continue
        # Overwrite athlete with the roster-mapped name (handles typos/caps)
        dfb["Athlete"] = mapped
        blast_frames.append(dfb)

    blast_all = pd.concat(blast_frames, ignore_index=True) if blast_frames else \
        pd.DataFrame(columns=BLAST_HEADER_SIGNATURE + ["Athlete","Blast_Timestamp","__source_file"])

    # Per-athlete processing
    summary = []
    for athlete in roster:
        info(f"\n🏃 Processing {athlete}...")
        h = hittrax[hittrax["Athlete"] == athlete].copy()
        b = blast_all[blast_all["Athlete"] == athlete].copy()

        info(f"   📊 HitTrax rows: {len(h)} | Blast rows: {len(b)}")

        # Build matches
        if not b.empty and not h.empty:
            matched = asof_match(b, h, window_seconds=TIME_WINDOW_SEC)
        else:
            matched = pd.DataFrame()

        matches_count = len(matched)
        hi = (matched["Confidence"] == "High").sum() if not matched.empty else 0
        md = (matched["Confidence"] == "Medium").sum() if not matched.empty else 0
        info(f"   ✅ Found {matches_count} matches ({hi} high, {md} medium)")

        # Columns to bring from matched (NO 'Athlete' to avoid merge dup)
        keep_blast_cols = [c for c in BLAST_HEADER_SIGNATURE if c in b.columns]
        m_keep = ["Blast_Timestamp","HitTrax_Timestamp","Confidence","Match_Type","Time_Delta_Sec","__source_file"] + keep_blast_cols

        if not matched.empty:
            m = matched.loc[:, [c for c in m_keep if c in matched.columns]].copy()
            # Merge Blast details onto HitTrax by timestamp (left keeps all HitTrax)
            left = h.copy()
            right = m.copy()
        else:
            left = h.copy()
            right = pd.DataFrame(columns=m_keep)

        # --- Unmatched Blast rows (to keep Blast-only swings) ---
        if not b.empty:
            if not matched.empty:
                matched_b_times = set(pd.to_datetime(matched["Blast_Timestamp"]).dropna().astype("datetime64[ns]").unique())
                b_unmatched = b[~b["Blast_Timestamp"].isin(matched_b_times)].copy()
            else:
                b_unmatched = b.copy()
        else:
            b_unmatched = pd.DataFrame()

        # Prepare Blast-only rows in a schema that can normalize later
        blast_only_rows = []
        if not b_unmatched.empty:
            # For Blast-only rows, build a minimal frame with Blast data & metadata
            bo = b_unmatched.copy()
            bo["Match_Type"] = "Blast_Only"
            bo["Confidence"] = "Unmatched"
            bo["Time_Delta_Sec"] = pd.NA
            # No HitTrax timestamps for these
            bo["HitTrax_Timestamp"] = pd.NaT
            # Keep only useful columns; Athlete stays
            bo = bo.loc[:, ["Athlete","Blast_Timestamp","Match_Type","Confidence","Time_Delta_Sec","__source_file"] + keep_blast_cols]
            blast_only_rows.append(bo)

        # === Patch 3: make concat safe (unique columns per frame) ===
        left  = left.loc[:,  ~left.columns.duplicated()].copy()
        right = right.loc[:, ~right.columns.duplicated()].copy()

        # First: join HitTrax rows with matched Blast on timestamp
        merged_lr = pd.merge(left, right, how="left", on="HitTrax_Timestamp")

        # Second: append Blast-only rows (if any)
        frames = [merged_lr]
        for bo in blast_only_rows:
            frames.append(bo)

        clean_frames = []
        for df_ in frames:
            clean_frames.append(df_.loc[:, ~df_.columns.duplicated()].copy())

        combined = pd.concat(clean_frames, ignore_index=True)

        # Normalize and order columns for the per-athlete file
        final = normalize_and_order(combined)

        # Save per-athlete file
        safe_name = athlete.replace(" ", "_")
        out_path = out_folder / f"{safe_name}_complete_data.csv"
        final.to_csv(out_path, index=False)
        info(f"   💾 Saved {len(final)} records to {out_path}")

        summary.append([athlete, len(final), matches_count, len(b), len(h)])

    # Combined export
    all_exports = []
    for f in sorted(out_folder.glob("*_complete_data.csv")):
        if f.name.startswith("ALL_ATHLETES"): continue
        try:
            all_exports.append(pd.read_csv(f))
        except Exception:
            pass

    if all_exports:
        # Clean duplicates (safety) then concat and normalize
        cleaned = []
        for df_ in all_exports:
            cleaned.append(df_.loc[:, ~df_.columns.duplicated()].copy())

        all_combined = pd.concat(cleaned, ignore_index=True)
        all_combined = all_combined.loc[:, ~all_combined.columns.duplicated()].copy()
        all_combined = normalize_and_order(all_combined)

        out_all = out_folder / "ALL_ATHLETES_complete_data.csv"
        all_combined.to_csv(out_all, index=False)
        info(f"\n💾 Saved combined data: {len(all_combined)} total records to {out_all}")

    # Summary
    if summary:
        info("\n📊 Processing Summary:")
        info("Athlete                Total  Matched   Blast  HitTrax")
        info("---------------------------------------------------------------")
        for row in summary:
            info(f"{row[0]:<22} {row[1]:<6} {row[2]:<8} {row[3]:<6} {row[4]:<6}")
        info("---------------------------------------------------------------")
        total_rows = sum(r[1] for r in summary)
        total_matches = sum(r[2] for r in summary)
        info(f"TOTAL                  {total_rows:<6} {total_matches:<8}")

if __name__ == "__main__":
    pd.set_option("display.width", 200)
    pd.set_option("display.max_columns", 120)
    main()
